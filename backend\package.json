{"name": "khel-saathi-backend", "version": "1.0.0", "description": "Backend API for Khel-Saathi sports social network platform", "main": "src/server.js", "type": "module", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js", "build": "echo 'No build step required for Node.js'", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "seed": "node src/scripts/seedDatabase.js"}, "keywords": ["sports", "social-network", "api", "express", "mongodb", "socket.io"], "author": "Khel-Saathi Team", "license": "MIT", "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "redis": "^4.6.11", "socket.io": "^4.7.4", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@babel/preset-env": "^7.23.6", "babel-jest": "^29.7.0", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}