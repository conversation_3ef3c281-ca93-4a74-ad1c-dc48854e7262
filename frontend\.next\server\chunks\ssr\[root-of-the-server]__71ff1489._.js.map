{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/<PERSON><PERSON>-<PERSON><PERSON><PERSON>/frontend/src/app/page.tsx"], "sourcesContent": ["export default function Home() {\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"border-b\">\n        <div className=\"container mx-auto px-4 py-4 flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-primary rounded-lg flex items-center justify-center\">\n              <span className=\"text-primary-foreground font-bold\">K</span>\n            </div>\n            <span className=\"text-xl font-heading font-bold\">Khel-Saathi</span>\n          </div>\n          <div className=\"flex items-center space-x-4\">\n            <button className=\"px-4 py-2 border rounded-md\">Login</button>\n            <button className=\"px-4 py-2 bg-primary text-primary-foreground rounded-md\">Get Started</button>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"py-20 px-4\">\n        <div className=\"container mx-auto text-center\">\n          <div className=\"max-w-4xl mx-auto\">\n            <h1 className=\"text-4xl md:text-6xl font-heading font-bold mb-6 bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent\">\n              Connect. Play. Compete.\n            </h1>\n            <p className=\"text-xl md:text-2xl text-muted-foreground mb-8 max-w-2xl mx-auto\">\n              The ultimate sports social network for finding teammates, joining events, and building your sports community.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"px-8 py-3 bg-primary text-primary-foreground rounded-md text-lg\">\n                Start Playing\n              </button>\n              <button className=\"px-8 py-3 border rounded-md text-lg\">\n                Learn More\n              </button>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Sports Section */}\n      <section className=\"py-16 px-4 bg-muted/50\">\n        <div className=\"container mx-auto\">\n          <h2 className=\"text-3xl font-heading font-bold text-center mb-12\">\n            Popular Sports\n          </h2>\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4\">\n            {[\n              { name: \"Cricket\", icon: \"🏏\" },\n              { name: \"Football\", icon: \"⚽\" },\n              { name: \"Basketball\", icon: \"🏀\" },\n              { name: \"Badminton\", icon: \"🏸\" },\n              { name: \"Tennis\", icon: \"🎾\" },\n              { name: \"Volleyball\", icon: \"🏐\" },\n            ].map((sport) => (\n              <div key={sport.name} className=\"bg-card border rounded-lg p-6 text-center hover:shadow-lg transition-shadow cursor-pointer\">\n                <div className=\"text-4xl mb-2\">{sport.icon}</div>\n                <h3 className=\"font-semibold\">{sport.name}</h3>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-16 px-4\">\n        <div className=\"container mx-auto\">\n          <h2 className=\"text-3xl font-heading font-bold text-center mb-12\">\n            Why Choose Khel-Saathi?\n          </h2>\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {[\n              {\n                title: \"Find Teammates\",\n                description: \"Connect with players near you based on skill level and sports interests\"\n              },\n              {\n                title: \"Join Events\",\n                description: \"Discover and participate in local sports events and tournaments\"\n              },\n              {\n                title: \"Location-Based\",\n                description: \"Find players and events in your area with smart geolocation\"\n              },\n              {\n                title: \"Real-time Chat\",\n                description: \"Communicate with your team and organize matches instantly\"\n              },\n              {\n                title: \"Leaderboards\",\n                description: \"Track your progress and compete with other players\"\n              },\n              {\n                title: \"Rating System\",\n                description: \"Build your reputation through player ratings and reviews\"\n              }\n            ].map((feature) => (\n              <div key={feature.title} className=\"bg-card border rounded-lg p-6 hover:shadow-lg transition-shadow\">\n                <h3 className=\"text-xl font-semibold mb-4\">{feature.title}</h3>\n                <p className=\"text-muted-foreground\">{feature.description}</p>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 px-4 bg-primary text-primary-foreground\">\n        <div className=\"container mx-auto text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-heading font-bold mb-6\">\n            Ready to Join the Game?\n          </h2>\n          <p className=\"text-xl mb-8 opacity-90 max-w-2xl mx-auto\">\n            Connect with thousands of sports enthusiasts, find your perfect teammates, and never miss a game again.\n          </p>\n          <button className=\"px-8 py-3 bg-secondary text-secondary-foreground rounded-md text-lg\">\n            Create Your Profile\n          </button>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"border-t py-12 px-4\">\n        <div className=\"container mx-auto\">\n          <div className=\"grid md:grid-cols-4 gap-8\">\n            <div>\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <div className=\"w-8 h-8 bg-primary rounded-lg flex items-center justify-center\">\n                  <span className=\"text-primary-foreground font-bold\">K</span>\n                </div>\n                <span className=\"text-xl font-heading font-bold\">Khel-Saathi</span>\n              </div>\n              <p className=\"text-muted-foreground\">\n                Building the future of sports communities, one connection at a time.\n              </p>\n            </div>\n            <div>\n              <h3 className=\"font-semibold mb-4\">Platform</h3>\n              <ul className=\"space-y-2 text-muted-foreground\">\n                <li>Find Players</li>\n                <li>Join Events</li>\n                <li>Create Teams</li>\n                <li>Leaderboards</li>\n              </ul>\n            </div>\n            <div>\n              <h3 className=\"font-semibold mb-4\">Sports</h3>\n              <ul className=\"space-y-2 text-muted-foreground\">\n                <li>Cricket</li>\n                <li>Football</li>\n                <li>Basketball</li>\n                <li>Badminton</li>\n              </ul>\n            </div>\n            <div>\n              <h3 className=\"font-semibold mb-4\">Support</h3>\n              <ul className=\"space-y-2 text-muted-foreground\">\n                <li>Help Center</li>\n                <li>Contact Us</li>\n                <li>Privacy Policy</li>\n                <li>Terms of Service</li>\n              </ul>\n            </div>\n          </div>\n          <div className=\"border-t mt-8 pt-8 text-center text-muted-foreground\">\n            <p>&copy; 2025 Khel-Saathi. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;8CAEtD,8OAAC;oCAAK,WAAU;8CAAiC;;;;;;;;;;;;sCAEnD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;8CAA8B;;;;;;8CAChD,8OAAC;oCAAO,WAAU;8CAA0D;;;;;;;;;;;;;;;;;;;;;;;0BAMlF,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2H;;;;;;0CAGzI,8OAAC;gCAAE,WAAU;0CAAmE;;;;;;0CAGhF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAAkE;;;;;;kDAGpF,8OAAC;wCAAO,WAAU;kDAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShE,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,MAAM;oCAAW,MAAM;gCAAK;gCAC9B;oCAAE,MAAM;oCAAY,MAAM;gCAAI;gCAC9B;oCAAE,MAAM;oCAAc,MAAM;gCAAK;gCACjC;oCAAE,MAAM;oCAAa,MAAM;gCAAK;gCAChC;oCAAE,MAAM;oCAAU,MAAM;gCAAK;gCAC7B;oCAAE,MAAM;oCAAc,MAAM;gCAAK;6BAClC,CAAC,GAAG,CAAC,CAAC,sBACL,8OAAC;oCAAqB,WAAU;;sDAC9B,8OAAC;4CAAI,WAAU;sDAAiB,MAAM,IAAI;;;;;;sDAC1C,8OAAC;4CAAG,WAAU;sDAAiB,MAAM,IAAI;;;;;;;mCAFjC,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;0BAU5B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,OAAO;oCACP,aAAa;gCACf;gCACA;oCACE,OAAO;oCACP,aAAa;gCACf;gCACA;oCACE,OAAO;oCACP,aAAa;gCACf;gCACA;oCACE,OAAO;oCACP,aAAa;gCACf;gCACA;oCACE,OAAO;oCACP,aAAa;gCACf;gCACA;oCACE,OAAO;oCACP,aAAa;gCACf;6BACD,CAAC,GAAG,CAAC,CAAC,wBACL,8OAAC;oCAAwB,WAAU;;sDACjC,8OAAC;4CAAG,WAAU;sDAA8B,QAAQ,KAAK;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAyB,QAAQ,WAAW;;;;;;;mCAFjD,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;0BAU/B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmD;;;;;;sCAGjE,8OAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAGzD,8OAAC;4BAAO,WAAU;sCAAsE;;;;;;;;;;;;;;;;;0BAO5F,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;8DAEtD,8OAAC;oDAAK,WAAU;8DAAiC;;;;;;;;;;;;sDAEnD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAGR,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAGR,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;sCAIV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}]}