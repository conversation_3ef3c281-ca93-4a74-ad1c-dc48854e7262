"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { Star, Quote } from "lucide-react"

import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

const testimonials = [
  {
    name: "<PERSON><PERSON>",
    role: "Cricket Enthusiast",
    location: "Mumbai",
    avatar: "/placeholder-avatar-1.jpg",
    rating: 5,
    content: "Found my regular cricket team through <PERSON><PERSON>-<PERSON><PERSON><PERSON>. The matchmaking is spot-on and I've made some great friends. Now we play every weekend!",
    sport: "🏏"
  },
  {
    name: "<PERSON><PERSON>",
    role: "Football Player",
    location: "Bangalore",
    avatar: "/placeholder-avatar-2.jpg",
    rating: 5,
    content: "As a working professional, finding time and teammates for football was tough. This app made it so easy to connect with like-minded players.",
    sport: "⚽"
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    role: "Badminton Coach",
    location: "Delhi",
    avatar: "/placeholder-avatar-3.jpg",
    rating: 5,
    content: "I use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to organize tournaments and find new students. The event management features are fantastic and save me hours of work.",
    sport: "🏸"
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    role: "Basketball Player",
    location: "Hyderabad",
    avatar: "/placeholder-avatar-4.jpg",
    rating: 5,
    content: "The community here is amazing! I've improved my game significantly by playing with different skill levels. The rating system keeps it fair.",
    sport: "🏀"
  },
  {
    name: "Vikram Kumar",
    role: "Tennis Player",
    location: "Chennai",
    avatar: "/placeholder-avatar-5.jpg",
    rating: 5,
    content: "Love the real-time chat feature. Coordinating matches and court bookings with my tennis group has never been easier. Highly recommended!",
    sport: "🎾"
  },
  {
    name: "Anita Joshi",
    role: "Volleyball Captain",
    location: "Pune",
    avatar: "/placeholder-avatar-6.jpg",
    rating: 5,
    content: "Managing our volleyball team became so much simpler. The app helps us find substitute players quickly and keeps everyone updated.",
    sport: "🏐"
  }
]

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
}

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 }
}

export function TestimonialsSection() {
  return (
    <section className="py-20">
      <div className="container">
        <div className="mx-auto max-w-3xl text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-3xl font-bold tracking-tight sm:text-4xl mb-4"
          >
            What Our Players Say
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-lg text-muted-foreground"
          >
            Join thousands of satisfied players who have found their perfect sports community
          </motion.p>
        </div>

        <motion.div
          variants={container}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true }}
          className="grid gap-6 md:grid-cols-2 lg:grid-cols-3"
        >
          {testimonials.map((testimonial, index) => (
            <motion.div key={testimonial.name} variants={item}>
              <Card className="group h-full border-0 bg-gradient-to-br from-background to-muted/20 transition-all duration-300 hover:scale-105 hover:shadow-xl">
                <CardContent className="p-6">
                  {/* Quote Icon */}
                  <div className="mb-4 flex items-start justify-between">
                    <Quote className="h-8 w-8 text-primary/20" />
                    <div className="text-2xl">{testimonial.sport}</div>
                  </div>

                  {/* Rating */}
                  <div className="mb-4 flex space-x-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>

                  {/* Content */}
                  <p className="mb-6 text-muted-foreground leading-relaxed">
                    "{testimonial.content}"
                  </p>

                  {/* Author */}
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                      <AvatarFallback>
                        {testimonial.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-semibold text-sm">{testimonial.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {testimonial.role} • {testimonial.location}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="inline-flex items-center space-x-8 text-sm text-muted-foreground">
            <div className="flex items-center space-x-2">
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
              <span>4.9/5 Average Rating</span>
            </div>
            <div>10,000+ Happy Players</div>
            <div>50+ Cities Covered</div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
