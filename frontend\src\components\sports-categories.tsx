"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { TrendingUp } from "lucide-react"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

const sports = [
  { 
    name: "<PERSON>", 
    icon: "🏏", 
    players: "2.5K",
    events: "45",
    trending: true,
    gradient: "from-orange-500 to-red-500"
  },
  { 
    name: "Football", 
    icon: "⚽", 
    players: "1.8K",
    events: "38",
    trending: true,
    gradient: "from-green-500 to-emerald-500"
  },
  { 
    name: "<PERSON>", 
    icon: "🏀", 
    players: "1.2K",
    events: "25",
    trending: false,
    gradient: "from-orange-600 to-yellow-500"
  },
  { 
    name: "<PERSON><PERSON><PERSON>", 
    icon: "🏸", 
    players: "950",
    events: "32",
    trending: true,
    gradient: "from-pink-500 to-rose-500"
  },
  { 
    name: "Tennis", 
    icon: "🎾", 
    players: "780",
    events: "18",
    trending: false,
    gradient: "from-purple-500 to-violet-500"
  },
  { 
    name: "<PERSON>", 
    icon: "🏐", 
    players: "650",
    events: "22",
    trending: false,
    gradient: "from-blue-500 to-cyan-500"
  },
  { 
    name: "<PERSON>", 
    icon: "🏒", 
    players: "420",
    events: "12",
    trending: false,
    gradient: "from-slate-500 to-gray-500"
  },
  { 
    name: "Swimming", 
    icon: "🏊", 
    players: "380",
    events: "8",
    trending: false,
    gradient: "from-cyan-500 to-blue-500"
  },
]

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
}

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 }
}

export function SportsCategories() {
  return (
    <section className="py-20">
      <div className="container">
        <div className="mx-auto max-w-2xl text-center mb-12">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-3xl font-bold tracking-tight sm:text-4xl mb-4"
          >
            Popular Sports
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-lg text-muted-foreground"
          >
            Join thousands of players across different sports and skill levels
          </motion.p>
        </div>

        <motion.div
          variants={container}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true }}
          className="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4"
        >
          {sports.map((sport) => (
            <motion.div key={sport.name} variants={item}>
              <Card className="group relative overflow-hidden border-0 bg-gradient-to-br from-background to-muted/20 transition-all duration-300 hover:scale-105 hover:shadow-xl cursor-pointer">
                <CardContent className="p-6 text-center">
                  {/* Trending Badge */}
                  {sport.trending && (
                    <Badge 
                      variant="secondary" 
                      className="absolute top-2 right-2 text-xs"
                    >
                      <TrendingUp className="mr-1 h-3 w-3" />
                      Hot
                    </Badge>
                  )}

                  {/* Sport Icon with Gradient Background */}
                  <div className={`mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r ${sport.gradient} text-3xl shadow-lg`}>
                    {sport.icon}
                  </div>

                  {/* Sport Name */}
                  <h3 className="mb-3 text-lg font-semibold group-hover:text-primary transition-colors">
                    {sport.name}
                  </h3>

                  {/* Stats */}
                  <div className="space-y-1 text-sm text-muted-foreground">
                    <div className="flex justify-between">
                      <span>Players:</span>
                      <span className="font-medium">{sport.players}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Events:</span>
                      <span className="font-medium">{sport.events}</span>
                    </div>
                  </div>

                  {/* Hover Effect Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-blue-600/5 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* View All Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          viewport={{ once: true }}
          className="mt-12 text-center"
        >
          <button className="text-primary hover:text-primary/80 font-medium transition-colors">
            View All Sports →
          </button>
        </motion.div>
      </div>
    </section>
  )
}
