{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO initialized successfully\u001b[39m","timestamp":"2025-08-01 14:22:11:2211"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: localhost\u001b[39m","timestamp":"2025-08-01 14:22:11:2211"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:11:2211"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:11:2211"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:12:2212"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:12:2212"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:12:2212"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:12:2212"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:12:2212"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:13:2213"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:13:2213"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:13:2213"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:14:2214"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:14:2214"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:15:2215"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:15:2215"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:16:2216"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:16:2216"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:17:2217"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:17:2217"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:18:2218"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:18:2218"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:19:2219"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:19:2219"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:20:2220"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:20:2220"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:21:2221"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:21:2221"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:22:2222"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:22:2222"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:23:2223"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:23:2223"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:24:2224"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:25:2225"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:25:2225"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:26:2226"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:26:2226"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:27:2227"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:27:2227"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:28:2228"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:28:2228"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:29:2229"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:29:2229"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:30:2230"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:30:2230"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:31:2231"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:31:2231"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:32:2232"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:32:2232"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:33:2233"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:33:2233"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:34:2234"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:34:2234"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:35:2235"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:35:2235"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:36:2236"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:36:2236"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:37:2237"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:37:2237"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:38:2238"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:38:2238"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:39:2239"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:39:2239"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:40:2240"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:40:2240"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:41:2241"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:41:2241"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:42:2242"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:42:2242"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:43:2243"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:43:2243"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:44:2244"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:44:2244"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:45:2245"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:45:2245"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:46:2246"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:46:2246"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:47:2247"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:47:2247"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:48:2248"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:48:2248"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:49:2249"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:49:2249"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO initialized successfully\u001b[39m","timestamp":"2025-08-01 14:22:52:2252"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: localhost\u001b[39m","timestamp":"2025-08-01 14:22:52:2252"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB connected successfully\u001b[39m","timestamp":"2025-08-01 14:22:52:2252"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:52:2252"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:52:2252"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:52:2252"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:52:2252"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:52:2252"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:52:2252"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:53:2253"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:53:2253"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:53:2253"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:54:2254"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:54:2254"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:55:2255"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:55:2255"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:56:2256"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:56:2256"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:57:2257"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:57:2257"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:58:2258"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:58:2258"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:59:2259"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:22:59:2259"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:00:230"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:00:230"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:01:231"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:01:231"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:02:232"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:02:232"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:03:233"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:03:233"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:04:234"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:04:234"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:05:235"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:05:235"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:06:236"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:06:236"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:07:237"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:07:237"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:08:238"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:08:238"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:09:239"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:09:239"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:10:2310"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:10:2310"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:11:2311"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:11:2311"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:12:2312"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:12:2312"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:13:2313"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:13:2313"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:14:2314"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:14:2314"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:15:2315"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:15:2315"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:16:2316"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:16:2316"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:17:2317"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:17:2317"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:18:2318"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:18:2318"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:19:2319"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:19:2319"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:20:2320"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:20:2320"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:21:2321"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:21:2321"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:22:2322"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:22:2322"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:23:2323"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:23:2323"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:24:2324"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:24:2324"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:25:2325"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:26:2326"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:26:2326"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:27:2327"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:27:2327"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:28:2328"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:28:2328"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:29:2329"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:29:2329"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:30:2330"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:30:2330"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:31:2331"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:31:2331"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:32:2332"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:32:2332"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:33:2333"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:33:2333"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:34:2334"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:34:2334"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:35:2335"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:35:2335"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:36:2336"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:36:2336"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:37:2337"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:37:2337"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:38:2338"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:38:2338"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:39:2339"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:39:2339"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:40:2340"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:40:2340"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:41:2341"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:41:2341"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:42:2342"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:42:2342"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:43:2343"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:43:2343"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:44:2344"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:44:2344"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:45:2345"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:45:2345"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:46:2346"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:46:2346"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:47:2347"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:47:2347"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:48:2348"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:48:2348"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:49:2349"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:49:2349"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:50:2350"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:50:2350"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:51:2351"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:51:2351"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:23:52:2352"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO initialized successfully\u001b[39m","timestamp":"2025-08-01 14:24:51:2451"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: localhost\u001b[39m","timestamp":"2025-08-01 14:24:52:2452"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB connected successfully\u001b[39m","timestamp":"2025-08-01 14:24:52:2452"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:52:2452"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:52:2452"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:52:2452"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:52:2452"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:52:2452"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:52:2452"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:52:2452"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:53:2453"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:53:2453"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:53:2453"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:54:2454"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:54:2454"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:55:2455"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:55:2455"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:56:2456"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:56:2456"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:57:2457"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:57:2457"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:58:2458"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:58:2458"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:59:2459"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:24:59:2459"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:00:250"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:01:251"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:01:251"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:02:252"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:02:252"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:03:253"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:03:253"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:04:254"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:04:254"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:05:255"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:05:255"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:06:256"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:06:256"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:07:257"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:07:257"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:08:258"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:08:258"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:09:259"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:09:259"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:10:2510"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:10:2510"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:11:2511"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:11:2511"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:12:2512"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:12:2512"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:13:2513"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:13:2513"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:14:2514"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:14:2514"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:15:2515"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:15:2515"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:16:2516"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:16:2516"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:17:2517"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:17:2517"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:18:2518"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:18:2518"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:19:2519"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:19:2519"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:20:2520"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:20:2520"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:21:2521"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:21:2521"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:22:2522"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:22:2522"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:23:2523"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:23:2523"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:24:2524"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:24:2524"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:25:2525"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:25:2525"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:26:2526"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:26:2526"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:27:2527"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:27:2527"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:28:2528"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:28:2528"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:29:2529"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:29:2529"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:30:2530"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:30:2530"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:31:2531"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:31:2531"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:32:2532"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:33:2533"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:33:2533"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:34:2534"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:34:2534"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:35:2535"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:35:2535"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:36:2536"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:36:2536"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:37:2537"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:37:2537"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:38:2538"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:38:2538"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:39:2539"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:39:2539"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:40:2540"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:40:2540"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:41:2541"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:41:2541"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:42:2542"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:42:2542"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:43:2543"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:43:2543"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:44:2544"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:44:2544"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:45:2545"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:45:2545"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:46:2546"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:46:2546"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:47:2547"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:47:2547"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:48:2548"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:48:2548"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:49:2549"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:49:2549"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:50:2550"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:50:2550"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:51:2551"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:51:2551"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:52:2552"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:52:2552"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:53:2553"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:53:2553"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:54:2554"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:54:2554"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:55:2555"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:55:2555"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:56:2556"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:56:2556"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:57:2557"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:57:2557"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:58:2558"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:58:2558"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:59:2559"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:25:59:2559"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:00:260"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:00:260"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:01:261"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:01:261"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:02:262"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:02:262"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:03:263"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:04:264"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:04:264"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:05:265"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:05:265"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:06:266"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:06:266"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:07:267"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:07:267"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:08:268"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:08:268"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:09:269"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:09:269"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:10:2610"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:10:2610"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:11:2611"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:11:2611"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:12:2612"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:12:2612"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:13:2613"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:14:2614"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:14:2614"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:15:2615"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:15:2615"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:16:2616"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:16:2616"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:17:2617"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:17:2617"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:18:2618"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:18:2618"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:19:2619"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:19:2619"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:20:2620"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:20:2620"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:21:2621"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:21:2621"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:22:2622"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:22:2622"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:23:2623"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:23:2623"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:24:2624"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:24:2624"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:25:2625"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:25:2625"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRedis Client Error:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-08-01 14:26:26:2626"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO initialized successfully\u001b[39m","timestamp":"2025-08-01 14:26:29:2629"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: localhost\u001b[39m","timestamp":"2025-08-01 14:26:29:2629"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB connected successfully\u001b[39m","timestamp":"2025-08-01 14:26:29:2629"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:26:29:2629"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:26:29:2629"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:26:30:2630"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:26:30:2630"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:26:30:2630"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:26:30:2630"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:26:30:2630"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:26:31:2631"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:26:31:2631"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:26:31:2631"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:26:32:2632"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:26:32:2632"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis connection failed, continuing without cache:\u001b[39m","timestamp":"2025-08-01 14:26:32:2632"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔴 Redis connected successfully\u001b[39m","timestamp":"2025-08-01 14:26:32:2632"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5000\u001b[39m","timestamp":"2025-08-01 14:26:32:2632"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📱 Environment: development\u001b[39m","timestamp":"2025-08-01 14:26:32:2632"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌐 Frontend URL: http://localhost:3000\u001b[39m","timestamp":"2025-08-01 14:26:32:2632"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Health Check: http://localhost:5000/health\u001b[39m","timestamp":"2025-08-01 14:26:32:2632"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:26:33:2633"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:26:33:2633"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:26:34:2634"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:26:34:2634"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:26:35:2635"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:26:35:2635"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:26:36:2636"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO initialized successfully\u001b[39m","timestamp":"2025-08-01 14:27:09:279"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: localhost\u001b[39m","timestamp":"2025-08-01 14:27:09:279"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB connected successfully\u001b[39m","timestamp":"2025-08-01 14:27:09:279"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:09:279"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:09:279"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:09:279"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:09:279"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:09:279"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:10:2710"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:10:2710"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:10:2710"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:10:2710"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:11:2711"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:11:2711"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:12:2712"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis connection failed, continuing without cache:\u001b[39m","timestamp":"2025-08-01 14:27:12:2712"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔴 Redis connected successfully\u001b[39m","timestamp":"2025-08-01 14:27:12:2712"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5000\u001b[39m","timestamp":"2025-08-01 14:27:12:2712"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📱 Environment: development\u001b[39m","timestamp":"2025-08-01 14:27:12:2712"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌐 Frontend URL: http://localhost:3000\u001b[39m","timestamp":"2025-08-01 14:27:12:2712"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Health Check: http://localhost:5000/health\u001b[39m","timestamp":"2025-08-01 14:27:12:2712"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:12:2712"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:13:2713"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:13:2713"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:14:2714"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:14:2714"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:15:2715"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:15:2715"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:16:2716"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:16:2716"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:17:2717"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:17:2717"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:18:2718"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:18:2718"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:19:2719"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:19:2719"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:20:2720"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:20:2720"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:21:2721"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:21:2721"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:22:2722"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:22:2722"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:23:2723"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:24:2724"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:24:2724"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:25:2725"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:25:2725"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:26:2726"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:26:2726"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:27:2727"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:27:2727"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:28:2728"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:28:2728"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:29:2729"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:29:2729"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:30:2730"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:30:2730"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:31:2731"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:31:2731"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:32:2732"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:32:2732"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:33:2733"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:33:2733"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:34:2734"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:34:2734"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:35:2735"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:35:2735"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:36:2736"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:36:2736"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:37:2737"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:37:2737"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:38:2738"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:38:2738"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:39:2739"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:39:2739"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:40:2740"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:40:2740"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:41:2741"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:41:2741"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:42:2742"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:42:2742"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:43:2743"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:43:2743"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:44:2744"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:44:2744"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:45:2745"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:45:2745"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:46:2746"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:46:2746"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:47:2747"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:47:2747"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:48:2748"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:48:2748"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:49:2749"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:49:2749"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:50:2750"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:50:2750"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:51:2751"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:51:2751"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:52:2752"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:52:2752"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:53:2753"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:53:2753"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:54:2754"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:55:2755"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:55:2755"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:56:2756"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:56:2756"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:57:2757"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:57:2757"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:58:2758"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:58:2758"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:59:2759"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:27:59:2759"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:00:280"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:00:280"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:01:281"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:01:281"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:02:282"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:02:282"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:03:283"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:03:283"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:04:284"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:04:284"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:05:285"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:05:285"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:06:286"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:06:286"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:07:287"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:07:287"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:08:288"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:08:288"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:09:289"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:09:289"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:10:2810"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:10:2810"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:11:2811"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:11:2811"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:12:2812"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:12:2812"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:13:2813"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:13:2813"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:14:2814"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:14:2814"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:15:2815"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:15:2815"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:16:2816"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:16:2816"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:17:2817"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:17:2817"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:18:2818"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:18:2818"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:19:2819"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:19:2819"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:20:2820"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:20:2820"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:21:2821"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:21:2821"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:22:2822"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:22:2822"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:23:2823"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:23:2823"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:24:2824"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:25:2825"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:25:2825"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:26:2826"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:26:2826"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:27:2827"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:27:2827"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:28:2828"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:28:2828"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:29:2829"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:29:2829"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:30:2830"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:30:2830"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:31:2831"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:31:2831"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:32:2832"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:32:2832"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:33:2833"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:33:2833"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:34:2834"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:34:2834"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:35:2835"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:35:2835"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:36:2836"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:36:2836"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:37:2837"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:37:2837"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:38:2838"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:38:2838"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:39:2839"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:39:2839"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:40:2840"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:40:2840"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:41:2841"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:41:2841"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:42:2842"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:42:2842"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:43:2843"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:43:2843"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:44:2844"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:44:2844"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:45:2845"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:45:2845"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:46:2846"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:46:2846"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:47:2847"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:47:2847"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:48:2848"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:48:2848"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:49:2849"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:49:2849"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:50:2850"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:50:2850"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:51:2851"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:51:2851"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:52:2852"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:52:2852"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:53:2853"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:53:2853"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:54:2854"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:54:2854"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:55:2855"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:56:2856"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:56:2856"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:57:2857"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:57:2857"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:58:2858"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:58:2858"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:59:2859"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:28:59:2859"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:00:290"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:00:290"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:01:291"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:01:291"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:02:292"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:02:292"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:03:293"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:03:293"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:04:294"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:04:294"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:05:295"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:05:295"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:06:296"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:06:296"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:07:297"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:07:297"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:08:298"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:08:298"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:09:299"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:09:299"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:10:2910"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:10:2910"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:11:2911"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:11:2911"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:12:2912"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:12:2912"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:13:2913"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:13:2913"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:14:2914"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:14:2914"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:15:2915"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:15:2915"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:16:2916"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:16:2916"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:17:2917"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:17:2917"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:18:2918"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:18:2918"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:19:2919"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:19:2919"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:20:2920"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:20:2920"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:21:2921"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:21:2921"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:22:2922"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:23:2923"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:23:2923"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:24:2924"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:24:2924"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:25:2925"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:25:2925"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:26:2926"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:26:2926"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:27:2927"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:27:2927"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:28:2928"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:28:2928"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:29:2929"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:29:2929"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:30:2930"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:30:2930"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:31:2931"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:31:2931"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:32:2932"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:32:2932"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:33:2933"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:33:2933"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:34:2934"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:34:2934"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:35:2935"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:35:2935"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:36:2936"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:36:2936"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:37:2937"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:37:2937"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:38:2938"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:38:2938"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:39:2939"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:39:2939"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:40:2940"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:40:2940"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:41:2941"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:41:2941"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:42:2942"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:42:2942"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:43:2943"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:43:2943"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:44:2944"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:44:2944"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:45:2945"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:45:2945"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:46:2946"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:46:2946"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:47:2947"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:47:2947"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:48:2948"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:48:2948"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:49:2949"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:49:2949"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:50:2950"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:50:2950"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:51:2951"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:51:2951"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:52:2952"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:53:2953"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:53:2953"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:54:2954"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:54:2954"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:55:2955"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:55:2955"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:56:2956"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:56:2956"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:57:2957"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:57:2957"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:58:2958"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:58:2958"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:59:2959"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:29:59:2959"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:00:300"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:00:300"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:01:301"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:01:301"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:02:302"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:02:302"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:03:303"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:03:303"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:04:304"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:04:304"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:05:305"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:05:305"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:06:306"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:06:306"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:07:307"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:07:307"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:08:308"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:08:308"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:09:309"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:09:309"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:10:3010"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:10:3010"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:11:3011"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:11:3011"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:12:3012"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:12:3012"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:13:3013"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:13:3013"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:14:3014"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:14:3014"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:15:3015"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:15:3015"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:16:3016"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:16:3016"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:17:3017"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:17:3017"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:18:3018"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:18:3018"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:19:3019"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:19:3019"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [01/Aug/2025:09:00:20 +0000] \"GET /health HTTP/1.1\" 200 103 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-IN) WindowsPowerShell/5.1.26100.4652\"\u001b[39m","timestamp":"2025-08-01 14:30:20:3020"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:20:3020"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:20:3020"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:21:3021"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:21:3021"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:22:3022"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:23:3023"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:23:3023"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:24:3024"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:24:3024"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:25:3025"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:25:3025"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:26:3026"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:26:3026"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:27:3027"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:27:3027"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:28:3028"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:28:3028"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:29:3029"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:29:3029"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:30:3030"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:30:3030"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:31:3031"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:31:3031"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:32:3032"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:32:3032"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:33:3033"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:33:3033"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:34:3034"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:34:3034"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:35:3035"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:35:3035"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:36:3036"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:36:3036"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:37:3037"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:37:3037"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:38:3038"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:38:3038"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:39:3039"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:39:3039"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:40:3040"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:40:3040"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:41:3041"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:41:3041"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:42:3042"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:42:3042"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:43:3043"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:43:3043"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:44:3044"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:44:3044"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:45:3045"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:45:3045"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:46:3046"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:46:3046"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:47:3047"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:47:3047"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:48:3048"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:48:3048"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:49:3049"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:49:3049"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:50:3050"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:50:3050"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:51:3051"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:51:3051"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:52:3052"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:52:3052"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:53:3053"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:53:3053"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:54:3054"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:55:3055"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:55:3055"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:56:3056"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:56:3056"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:57:3057"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:57:3057"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:58:3058"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:58:3058"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:59:3059"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:30:59:3059"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:00:310"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:00:310"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:01:311"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:01:311"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:02:312"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:02:312"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:03:313"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:03:313"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:04:314"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:04:314"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:05:315"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:05:315"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:06:316"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:06:316"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:07:317"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:07:317"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:08:318"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:08:318"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:09:319"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:09:319"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:10:3110"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:10:3110"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:11:3111"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:11:3111"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:12:3112"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:12:3112"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:13:3113"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:13:3113"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:14:3114"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:14:3114"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:15:3115"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:15:3115"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:16:3116"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:16:3116"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:17:3117"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:17:3117"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:18:3118"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:18:3118"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:19:3119"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:19:3119"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:20:3120"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:20:3120"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:21:3121"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:21:3121"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:22:3122"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:22:3122"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:23:3123"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:24:3124"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:24:3124"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:25:3125"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:25:3125"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:26:3126"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:26:3126"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:27:3127"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:27:3127"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:28:3128"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:28:3128"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:29:3129"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:29:3129"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:30:3130"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:30:3130"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:31:3131"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:31:3131"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:32:3132"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:32:3132"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:33:3133"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:33:3133"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:34:3134"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:34:3134"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:35:3135"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:35:3135"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:36:3136"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:36:3136"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:37:3137"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:37:3137"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:38:3138"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:38:3138"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:39:3139"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:39:3139"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:40:3140"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:40:3140"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:41:3141"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:41:3141"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:42:3142"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:42:3142"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:43:3143"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:43:3143"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:44:3144"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:44:3144"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:45:3145"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:45:3145"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:46:3146"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:46:3146"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:47:3147"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:47:3147"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:48:3148"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:48:3148"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:49:3149"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:49:3149"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:50:3150"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:50:3150"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:51:3151"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:51:3151"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:52:3152"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:52:3152"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:53:3153"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:54:3154"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:54:3154"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:55:3155"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:55:3155"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:56:3156"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:56:3156"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:57:3157"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:57:3157"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:58:3158"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:58:3158"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:59:3159"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:31:59:3159"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:00:320"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:00:320"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:01:321"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:01:321"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:02:322"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:02:322"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:03:323"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:03:323"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:04:324"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:04:324"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:05:325"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:05:325"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:06:326"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:06:326"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:07:327"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:07:327"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:08:328"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:08:328"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:09:329"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:09:329"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:10:3210"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:10:3210"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:11:3211"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:11:3211"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:12:3212"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:12:3212"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:13:3213"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:13:3213"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:14:3214"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:14:3214"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:15:3215"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:15:3215"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:16:3216"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:16:3216"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:17:3217"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:17:3217"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:18:3218"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:18:3218"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:19:3219"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:19:3219"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:20:3220"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:20:3220"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:21:3221"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:21:3221"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:22:3222"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:22:3222"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:23:3223"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:24:3224"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:24:3224"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:25:3225"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:25:3225"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:26:3226"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:26:3226"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:27:3227"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:27:3227"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:28:3228"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:28:3228"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:29:3229"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:29:3229"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:30:3230"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:30:3230"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:31:3231"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:31:3231"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:32:3232"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:32:3232"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:33:3233"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:33:3233"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:34:3234"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:34:3234"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:35:3235"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:35:3235"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:36:3236"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:36:3236"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:37:3237"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:37:3237"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:38:3238"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:38:3238"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:39:3239"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:39:3239"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:40:3240"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:40:3240"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:41:3241"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:41:3241"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:42:3242"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:42:3242"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:43:3243"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:43:3243"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:44:3244"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:44:3244"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:45:3245"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:45:3245"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:46:3246"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:46:3246"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:47:3247"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:47:3247"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:48:3248"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:48:3248"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:49:3249"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:49:3249"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:50:3250"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:50:3250"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:51:3251"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:51:3251"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:52:3252"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:52:3252"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:53:3253"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:54:3254"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:54:3254"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:55:3255"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:55:3255"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:56:3256"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:56:3256"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:57:3257"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:57:3257"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:58:3258"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:58:3258"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:59:3259"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:32:59:3259"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:00:330"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:00:330"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:01:331"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:01:331"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:02:332"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:02:332"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:03:333"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:03:333"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:04:334"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:04:334"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:05:335"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:05:335"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:06:336"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:06:336"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:07:337"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:07:337"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:08:338"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:08:338"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:09:339"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:09:339"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:10:3310"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:10:3310"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:11:3311"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:11:3311"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:12:3312"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:12:3312"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:13:3313"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:13:3313"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:14:3314"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:14:3314"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:15:3315"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:15:3315"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:16:3316"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:16:3316"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:17:3317"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:17:3317"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:18:3318"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:18:3318"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:19:3319"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:20:3320"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:20:3320"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:21:3321"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:21:3321"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:22:3322"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:22:3322"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:23:3323"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:23:3323"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:24:3324"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:24:3324"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:25:3325"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:25:3325"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:26:3326"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:26:3326"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:27:3327"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:27:3327"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:28:3328"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:28:3328"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:29:3329"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:29:3329"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:30:3330"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:30:3330"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:31:3331"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:31:3331"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:32:3332"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:32:3332"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:33:3333"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:33:3333"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:34:3334"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:34:3334"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:35:3335"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:35:3335"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:36:3336"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:36:3336"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:37:3337"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:37:3337"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:38:3338"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:38:3338"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:39:3339"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:39:3339"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:40:3340"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:40:3340"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:41:3341"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:41:3341"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:42:3342"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:42:3342"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:43:3343"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:43:3343"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:44:3344"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:44:3344"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:45:3345"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:33:45:3345"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:20:3420"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:20:3420"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:21:3421"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:21:3421"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:22:3422"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:23:3423"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:23:3423"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:24:3424"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:24:3424"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:25:3425"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:25:3425"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:26:3426"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:26:3426"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:27:3427"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:27:3427"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:28:3428"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:28:3428"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:29:3429"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:29:3429"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:30:3430"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:30:3430"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:31:3431"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:32:3432"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:32:3432"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:33:3433"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:33:3433"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:34:3434"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:34:3434"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:35:3435"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:35:3435"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:36:3436"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:36:3436"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:37:3437"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:37:3437"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:38:3438"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:38:3438"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:39:3439"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:39:3439"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:40:3440"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:40:3440"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:41:3441"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:41:3441"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:42:3442"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:42:3442"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:43:3443"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:43:3443"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:45:3445"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:45:3445"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:46:3446"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:46:3446"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:47:3447"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:47:3447"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:48:3448"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:48:3448"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:49:3449"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:49:3449"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:50:3450"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:50:3450"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:51:3451"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:51:3451"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:52:3452"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:52:3452"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:53:3453"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:53:3453"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:54:3454"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:54:3454"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:55:3455"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:55:3455"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:56:3456"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:56:3456"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:57:3457"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:57:3457"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:58:3458"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:58:3458"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:34:59:3459"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:00:350"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:00:350"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:01:351"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:01:351"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:02:352"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:02:352"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:03:353"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:03:353"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:04:354"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:04:354"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:05:355"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:05:355"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:06:356"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:06:356"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:07:357"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:07:357"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:08:358"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:08:358"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:09:359"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:09:359"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:10:3510"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:10:3510"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:11:3511"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:11:3511"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:12:3512"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:12:3512"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:13:3513"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:13:3513"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:14:3514"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:14:3514"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:15:3515"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:15:3515"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:16:3516"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:16:3516"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:17:3517"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:17:3517"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:18:3518"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:18:3518"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:19:3519"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:19:3519"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:20:3520"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:20:3520"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:21:3521"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:21:3521"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:22:3522"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:22:3522"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:23:3523"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:23:3523"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:24:3524"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:24:3524"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:25:3525"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:25:3525"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:26:3526"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:26:3526"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:27:3527"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:27:3527"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:28:3528"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:28:3528"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:29:3529"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:30:3530"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:30:3530"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:31:3531"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:31:3531"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:32:3532"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:32:3532"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:33:3533"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:33:3533"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:34:3534"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:34:3534"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:35:3535"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:35:3535"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:36:3536"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:36:3536"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:37:3537"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:37:3537"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:38:3538"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:38:3538"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:39:3539"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:39:3539"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:40:3540"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:40:3540"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:41:3541"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:41:3541"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:42:3542"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:42:3542"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:43:3543"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:43:3543"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:44:3544"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:44:3544"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:45:3545"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:45:3545"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:46:3546"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:47:3547"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:47:3547"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:48:3548"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:48:3548"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:49:3549"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:49:3549"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:50:3550"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:50:3550"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:51:3551"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:51:3551"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:52:3552"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:52:3552"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:53:3553"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:53:3553"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:54:3554"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:54:3554"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:55:3555"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:55:3555"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:56:3556"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:56:3556"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:57:3557"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:58:3558"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:58:3558"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:59:3559"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:35:59:3559"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:00:360"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:00:360"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:01:361"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:01:361"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:02:362"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:02:362"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:03:363"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:03:363"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:04:364"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:04:364"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:05:365"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:05:365"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:06:366"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:07:367"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:07:367"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:08:368"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:08:368"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:09:369"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:09:369"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:10:3610"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:10:3610"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:11:3611"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:11:3611"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:12:3612"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:12:3612"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:13:3613"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:13:3613"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:14:3614"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:14:3614"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:15:3615"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:15:3615"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:16:3616"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:16:3616"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:17:3617"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:17:3617"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:18:3618"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:18:3618"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:19:3619"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:19:3619"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:20:3620"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:21:3621"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:21:3621"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:22:3622"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:22:3622"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:23:3623"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:23:3623"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:24:3624"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:24:3624"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:25:3625"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:25:3625"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:26:3626"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:26:3626"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:27:3627"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:27:3627"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:28:3628"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:28:3628"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:29:3629"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:29:3629"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:30:3630"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:30:3630"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:31:3631"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:31:3631"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:32:3632"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:32:3632"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:33:3633"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:33:3633"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:34:3634"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:34:3634"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:35:3635"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:35:3635"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:36:3636"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:36:3636"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:37:3637"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:37:3637"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:38:3638"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:38:3638"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:39:3639"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:39:3639"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:40:3640"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:40:3640"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:41:3641"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:42:3642"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:42:3642"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:43:3643"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:43:3643"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:44:3644"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:44:3644"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:45:3645"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:45:3645"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:46:3646"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:46:3646"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:47:3647"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:47:3647"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:48:3648"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:48:3648"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:49:3649"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO initialized successfully\u001b[39m","timestamp":"2025-08-01 14:36:54:3654"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: localhost\u001b[39m","timestamp":"2025-08-01 14:36:54:3654"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB connected successfully\u001b[39m","timestamp":"2025-08-01 14:36:54:3654"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:54:3654"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:54:3654"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:54:3654"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:54:3654"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:54:3654"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:55:3655"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:55:3655"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:55:3655"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:56:3656"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:56:3656"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:56:3656"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:57:3657"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis connection failed, continuing without cache:\u001b[39m","timestamp":"2025-08-01 14:36:57:3657"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔴 Redis connected successfully\u001b[39m","timestamp":"2025-08-01 14:36:57:3657"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5000\u001b[39m","timestamp":"2025-08-01 14:36:57:3657"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📱 Environment: development\u001b[39m","timestamp":"2025-08-01 14:36:57:3657"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌐 Frontend URL: http://localhost:3000\u001b[39m","timestamp":"2025-08-01 14:36:57:3657"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Health Check: http://localhost:5000/health\u001b[39m","timestamp":"2025-08-01 14:36:57:3657"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:58:3658"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:58:3658"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:59:3659"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:36:59:3659"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:00:370"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:00:370"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:01:371"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:01:371"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:02:372"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:02:372"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:03:373"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:03:373"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:04:374"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:04:374"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:05:375"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:05:375"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:06:376"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:06:376"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:07:377"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:07:377"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:08:378"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:08:378"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:09:379"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:09:379"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:10:3710"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:10:3710"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:11:3711"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:11:3711"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:12:3712"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:12:3712"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:13:3713"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:13:3713"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:14:3714"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:14:3714"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:15:3715"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:15:3715"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:16:3716"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:16:3716"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:17:3717"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:17:3717"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:18:3718"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:18:3718"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:19:3719"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:19:3719"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:20:3720"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:20:3720"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:21:3721"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:21:3721"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:22:3722"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:22:3722"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:23:3723"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:23:3723"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:24:3724"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:24:3724"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:25:3725"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:25:3725"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:26:3726"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:26:3726"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:27:3727"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:28:3728"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:28:3728"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:29:3729"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:29:3729"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:30:3730"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:30:3730"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:31:3731"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:31:3731"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:32:3732"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:32:3732"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:33:3733"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:33:3733"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:34:3734"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:34:3734"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:35:3735"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:35:3735"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:36:3736"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:36:3736"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:37:3737"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:37:3737"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:38:3738"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:38:3738"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:39:3739"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:39:3739"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:40:3740"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:40:3740"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:41:3741"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:41:3741"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mRedis Client Error (continuing without Redis):\u001b[39m","timestamp":"2025-08-01 14:37:42:3742"}
