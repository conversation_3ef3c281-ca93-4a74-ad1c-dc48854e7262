"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { 
  Users, 
  Calendar, 
  MapPin, 
  MessageCircle, 
  Trophy, 
  Star,
  Zap,
  Shield,
  Heart
} from "lucide-react"

import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"

const features = [
  {
    icon: Users,
    title: "Smart Matchmaking",
    description: "AI-powered algorithm finds teammates based on skill level, location, and playing style preferences.",
    color: "from-blue-500 to-cyan-500"
  },
  {
    icon: Calendar,
    title: "Event Management",
    description: "Create, discover, and join sports events with RSVP tracking and automated reminders.",
    color: "from-green-500 to-emerald-500"
  },
  {
    icon: MapPin,
    title: "Location-Based Discovery",
    description: "Find players and events near you with precise geolocation and radius filtering.",
    color: "from-orange-500 to-red-500"
  },
  {
    icon: MessageCircle,
    title: "Real-time Chat",
    description: "Instant messaging with team coordination features and group chat capabilities.",
    color: "from-purple-500 to-violet-500"
  },
  {
    icon: Trophy,
    title: "Leaderboards & Rankings",
    description: "Track your progress with detailed statistics and compete on sport-specific leaderboards.",
    color: "from-yellow-500 to-orange-500"
  },
  {
    icon: Star,
    title: "Rating System",
    description: "Build your reputation through player ratings and verified skill assessments.",
    color: "from-pink-500 to-rose-500"
  },
  {
    icon: Zap,
    title: "Instant Notifications",
    description: "Real-time updates for match invites, event changes, and team communications.",
    color: "from-indigo-500 to-blue-500"
  },
  {
    icon: Shield,
    title: "Verified Profiles",
    description: "Enhanced security with profile verification and trusted player badges.",
    color: "from-teal-500 to-cyan-500"
  },
  {
    icon: Heart,
    title: "Community Building",
    description: "Join sports communities, follow favorite players, and build lasting connections.",
    color: "from-red-500 to-pink-500"
  }
]

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
}

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 }
}

export function FeaturesSection() {
  return (
    <section className="py-20 bg-muted/30">
      <div className="container">
        <div className="mx-auto max-w-3xl text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-3xl font-bold tracking-tight sm:text-4xl mb-4"
          >
            Why Choose Khel-Saathi?
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-lg text-muted-foreground"
          >
            Everything you need to connect with sports enthusiasts, organize events, 
            and build your athletic community in one powerful platform.
          </motion.p>
        </div>

        <motion.div
          variants={container}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true }}
          className="grid gap-6 md:grid-cols-2 lg:grid-cols-3"
        >
          {features.map((feature, index) => (
            <motion.div key={feature.title} variants={item}>
              <Card className="group h-full border-0 bg-background/60 backdrop-blur-sm transition-all duration-300 hover:scale-105 hover:shadow-xl">
                <CardHeader className="pb-4">
                  <div className={`inline-flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-r ${feature.color} mb-4`}>
                    <feature.icon className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle className="text-xl group-hover:text-primary transition-colors">
                    {feature.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="inline-flex items-center space-x-2 text-sm text-muted-foreground">
            <span>And many more features coming soon...</span>
            <span className="animate-pulse">✨</span>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
